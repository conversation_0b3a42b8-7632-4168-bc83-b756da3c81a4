module.exports = {
  apps: [
    {
      name: "CRON_LOG_HANDLER",
      script: "./server-logs.js",           // File JS cần chạy
      namespace: "logmove",
      cron_restart: "0 2 * * *",            // Chạy lúc 2:00 sáng hàng ngày
     // cron_restart: "*/1 * * * *",           // 🔁 Chạy mỗi 3 phút
      exec_mode: "fork",                    // fork mode vì script tự kết thúc
      instances: 1,
      max_memory_restart: "300M",           // Giới hạn RAM nếu cần
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      out_file: "./logs/out.log",
      error_file: "./logs/error.log",
      merge_logs: true,
      autorestart: false,
      env: {
        MAX_BACKUP_DAYS: "30",
        USER_LOG_FOLDERS: "/home/<USER>/datalog",
    }
	}
  ]
};
