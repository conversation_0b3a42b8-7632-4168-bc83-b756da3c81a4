require('dotenv').config(); // ⬅️ Load biến env
const fs = require('fs-extra');
const path = require('path');
const tar = require('tar');
const dayjs = require('dayjs');
const customParseFormat = require('dayjs/plugin/customParseFormat');
dayjs.extend(customParseFormat);

// ===============================
// CẤU HÌNH
// ===============================

const userLogFolders = (process.env.USER_LOG_FOLDERS || '')
  .split(':')
  .map(folder => folder.trim())
  .filter(folder => folder.length > 0);
console.log('userLogFolders: ', userLogFolders);

// const outputTarDir = './backup/log-archives';
// const outputTarDir = '../../datalog';
const outputTarDir = '../../datalog';
const outputLogFile = './logs/log_compression.log';

const timestamp = dayjs().format('YYYYMMDD_HHmmss');
const MAX_BACKUP_DAYS = parseInt(process.env.MAX_BACKUP_DAYS || '30', 30);
console.log('process.MAX_BACKUP_DAYS: ', process.env.MAX_BACKUP_DAYS);
console.log('process.USER_LOG_FOLDERS: ', process.env.USER_LOG_FOLDERS);

// ========================
// GHI LOG
// ========================
function writeLog(message) {
  const time = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const full = `[${time}] ${message}\n`;
  fs.appendFileSync(outputLogFile, full);
  console.log(message);
}

// ========================
// XOÁ FILE BACKUP CŨ
// ========================
async function deleteOldBackups() {
  const files = await fs.readdir(outputTarDir);
  const now = dayjs();

  for (const file of files) {

    // Tìm phần YYYYMMDD trong tên file
    const match = file.match(/_(\d{8})/);
    if (!match) continue;

    // kiểm tra ekyc
    const fileDateStr = match[1];
    const formatted = dayjs(fileDateStr, 'DDMMYYYY').format('YYYYMMDD');
    const fileDate = dayjs(formatted, 'YYYYMMDD');


    if (!fileDate.isValid()) {
      writeLog(`⚠️ Không thể parse ngày từ file: ${file}`);
      continue;
    }

    if (now.diff(fileDate, 'day') >= MAX_BACKUP_DAYS) {
      const fullPath = path.join(outputTarDir, file);
      try {
        await fs.remove(fullPath);
        writeLog(`🗑️ Xoá file backup quá hạn: ${file}`);
      } catch (err) {
        writeLog(`❌ Lỗi xoá file ${file}: ${err.message}`);
      }
    }
  }
}

// ========================
// (Phần compress giữ nguyên như trước)
// ========================
// Gọi `deleteOldBackups()` sau cùng

async function compressLogs() {

  // Đảm bảo thư mục backup và thư mục chứa file log tồn tại
  await fs.ensureDir(path.dirname(outputTarDir));
  await fs.ensureDir(outputTarDir);
  fs.ensureDirSync(path.dirname(outputLogFile));
  writeLog(`|-------------------- 🔁 BẮT ĐẦU XỬ LÝ: ${dayjs().format('YYYY-MM-DD')} --------------------|`);
  console.log('userLogFolders: ', userLogFolders);
  
  for (const baseFolder of userLogFolders) {
    if (!await fs.pathExists(baseFolder)) {
      writeLog(`[${dayjs().format('YYYY-MM-DD hh:mm:ss:SSS')}] ⚠️ Không tìm thấy folder: ${baseFolder}`);
      continue;
    }

    const userName = baseFolder.split('/').filter(Boolean).at(-2) || 'log';
    const entries = await fs.readdir(baseFolder);

    if (entries.some(e => /^\d{8}$/.test(e))) {
      writeLog(`[${dayjs().format('YYYY-MM-DD hh:mm:ss:SSS')}] THỰC HIỆN FOLDER: ${baseFolder}`);
      await compressFolderLogs(baseFolder, userName);
    }

    if (entries.some(e => /^[a-z]+_\d{8}\.log$/.test(e))) {
      writeLog(`[${dayjs().format('YYYY-MM-DD hh:mm:ss:SSS')}] THỰC HIỆN FILE: ${baseFolder}`);
      await compressSingleLogFiles(baseFolder, userName);
    }
  }

  await deleteOldBackups();

  writeLog('|-------------------- ✅ HOÀN TẤT ------------------------------------|\n\n');
}

async function compressFolderLogs(baseFolder, userName) {
  const entries = await fs.readdir(baseFolder);
  const today = dayjs();

  for (const entry of entries) {
    if (!/^\d{8}$/.test(entry)) continue;

    const entryDate = dayjs(entry, 'YYYYMMDD');
    if (!entryDate.isValid() || !entryDate.isBefore(today, 'day')) continue;

    const folderToCompress = path.join(baseFolder, entry);
    const tarName = `${userName}_${entry}.tar.gz`;
    const tarPath = path.join(outputTarDir, tarName);

    try {
      await tar.create(
        {
          gzip: true,
          file: tarPath,
          cwd: baseFolder,
        },
        [entry]
      );
      writeLog(`✅ Đã nén folder ${entry} thành ${tarName}`);

      await fs.remove(folderToCompress);
      writeLog(`🧹 Đã xoá folder gốc: ${folderToCompress}`);
      writeLog(`[${dayjs().format('YYYY-MM-DD hh:mm:ss:SSS')}] THÀNH CÔNG: ${tarName}`);
    } catch (err) {
      writeLog(`❌ Lỗi khi nén folder ${folderToCompress}: ${err.message}`);
    }
  }
}

async function deleteWebEkycOutFiles(baseFolder) {
  try {
    const allFiles = await fs.readdir(baseFolder);

    for (const file of allFiles) {
      if (file.startsWith('webekyc_out__')) {
        const filePath = path.join(baseFolder, file);
        await fs.unlink(filePath);
        console.log(`Đã xoá file webekyc_out__: ${file}`);
      }
    }
  } catch (error) {
    console.error('Lỗi khi xoá file webekyc_out__:', error);
  }
}

async function compressSingleLogFiles(baseFolder, userName) {
  const allFiles = await fs.readdir(baseFolder);
  const today = dayjs();
  console.log('compressSingleLogFiles: ', allFiles.length);

  // Xoá các file webekyc_out__ trước khi xử lý
  await deleteWebEkycOutFiles(baseFolder);

  // Nhóm theo ngày
  const filesByDate = {};

  for (const file of allFiles) {
    const match = file.match(/^([a-z]+)_(\d{8})\.log$/);
    if (!match) continue;
    const logDateStr = match[2];
    const formatted = dayjs(logDateStr, 'DDMMYYYY').format('YYYYMMDD');
    const logDate = dayjs(formatted, 'YYYYMMDD');

    if (!logDate.isValid() || !logDate.isBefore(today, 'day')) continue;

    if (!filesByDate[logDateStr]) {
      filesByDate[logDateStr] = [];
    }
    filesByDate[logDateStr].push(file);
  }

  for (const [dateStr, files] of Object.entries(filesByDate)) {
    const tarName = `${userName}_logs_${dateStr}.tar.gz`;
    const tarPath = path.join(outputTarDir, tarName);
    try {
      await tar.create(
        {
          gzip: true,
          file: tarPath,
          cwd: baseFolder
        },
        files
      );

      writeLog(`✅ Đã nén ${files.length} file log ngày ${dateStr} => ${tarName}`);
      for (const file of files) {
        await fs.remove(path.join(baseFolder, file));
        writeLog(`🧹 Đã xoá file log gốc: ${file}`);
      }
      writeLog(`[${dayjs().format('YYYY-MM-DD hh:mm:ss:SSS')}] THÀNH CÔNG: ${tarName}`);
    } catch (err) {
      writeLog(`❌ Lỗi khi nén log file ngày ${dateStr}: ${err.message}`);
    }
  }
}

compressLogs().then(() => {
  console.log('THOÁT PID');
  process.exit(0);
}).catch(err => {
  console.error(err);
  process.exit(1);
});
